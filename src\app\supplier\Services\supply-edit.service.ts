import { quoteData, sendNotifyDatamodel, supplies, Attachment } from './../Model/supplies';
import { SharedDataService } from './../../Common/Services/shared-data.service';
import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, forkJoin, map, Observable, of, switchMap, tap} from 'rxjs';
import { CoreDataService } from '../../Common/Services/core-data.service';

// Constants
const UPDATE_ACTION = 'update';

/**
 * Find the index of an item in an array by supplieID
 */
const itemIndex = (item: any, data: supplies[]): number => {
  for (let idx = 0; idx < data.length; idx++) {
    if (data[idx].supplieID === item.supplieID) {
      return idx;
    }
  }
  return -1;
};

/**
 * Create a deep copy of the data array
 */
const cloneData = (data: supplies[]) => data.map((item) => Object.assign({}, item));

@Injectable()
export class SupplyEditService extends BehaviorSubject<supplies[]> {
  // Flag to track if there are unsaved changes
  public change: boolean = false;

  // Current data being displayed/edited
  public data: supplies[] = [];

  // Original data for comparison and reset
  public originalData: supplies[] = [];

  // Items that have been created but not yet saved
  private createdItems: supplies[] = [];

  // Items that have been updated but not yet saved
  public updatedItems: supplies[] = [];

  // URL parameters
  public dataid: any;
  public secretid: any;
  public secretid1: any;

  // Counter for generating IDs
  public counter = 0;

  // ID of the inquiry quote being processed
  public inquiryQuoteid: any;

  // Type of validation being performed
  public validate: string = '';

  // Details of the quote being processed
  public detailsQuoteData: any = [];

//Hide Tier 2 field for Some cases
  public HideGridfield: boolean = false;

// Quote reference
public QuoteReference: string = '';


  constructor(protected sharedDataService: SharedDataService, protected coreData: CoreDataService) {
    super([]);

    // Extract URL parameters
    const values = window.location.search;
    const urlparams = new URLSearchParams(values);
    this.dataid = urlparams.get('data');
    this.secretid = urlparams.get('Secret');
    this.secretid1 = decodeURIComponent(urlparams.get('Secret') || '');

    // Store secret in local storage for later use
    localStorage.setItem("Secret", this.secretid1);
  }

  /**
   * Load data if not already loaded, or return existing data
   */
  public read() {
    // If data is already loaded, just emit it
    if (this.data.length) {
      return super.next(this.data);
    }

    // Otherwise fetch data from the API
    this.fetch().subscribe((data) => {
      this.data = data;
      this.originalData = cloneData(data); // Keep a copy for comparison/reset
      super.next(data);
    });
  }

  /**
   * Update an item in the data collection
   * Tracks changes for later saving
   */
  public update(item: supplies): void {
    if (!this.isNew(item)) {
      // For existing items, track in updatedItems array
      const index = itemIndex(item, this.updatedItems);
      if (index !== -1) {
        // Replace existing item in updatedItems
        this.updatedItems.splice(index, 1, item);
      } else {
        // Add to updatedItems
        this.updatedItems.push(item);
      }
    } else {
      // For new items, track in createdItems array
      const index = this.createdItems.indexOf(item);
      this.createdItems.splice(index, 1, item);
    }
  }

  /**
   * Check if an item is newly created
   */
  public isNew(item: any): boolean {
    return !item.supplieID;
  }

  /**
   * Check if there are unsaved changes
   */
  public hasChanges(): boolean {

    return this.change;
  }

  /**
   * Assign values from source object to target object
   */
  public assignValues(target: any, source: any): void {
    Object.assign(target, source);
  }
  //------------------------------------------------------
  // SAVE AND PERSISTENCE
  //------------------------------------------------------

  /**
   * Save all changes to the server
   * - Prepares data for submission
   * - Sends to API
   * - Updates local data with server response
   * - Sends notifications if needed
   */
  public saveChanges(): void {
    // Check if there are changes to save
    if (this.hasChanges()) {
      return;
    }

    // Process any updated items
    if (this.updatedItems.length) {
      this.processData(UPDATE_ACTION, this.updatedItems).subscribe(() => {
        // Processing complete
      });
    }

    // Prepare data for API submission
    const postedData = this.preparePostData();
    const sendnotifyData = this.notifyData();
    const sentMail = this.reply();

    // If no changes detected, show message and stop
    if (!postedData) {
      this.sharedDataService.showInfo("No changes detected.");
      return;
    }

    // Save data to the server
    this.coreData.saveSupplierResponses(postedData).pipe(
      // After saving, fetch updated data
      switchMap(() => {
        this.sharedDataService.showSuccess("Data Saved successfully ...");
        console.log("postedData", postedData);
        return this.fetch();
      }),

      // Update local data with server response
      tap((result: supplies[]) => {
        this.data = result;
        this.originalData = cloneData(result);
        super.next(this.data);
        this.reset();   // Reset tracking arrays
      }),

      // Send appropriate notifications based on validation type
      switchMap(() => {
        if (this.validate === 'InquiryQuote') {
          return this.coreData.sendNotification(sendnotifyData);
        }
        if (this.validate === 'ValidatePrice') {
          return this.coreData.replytoAccountManagerNOWPriceValid(sentMail);
        } else {
          return of(null);
        }
      }),
    ).subscribe({
      next: () => {
        console.log("Saved successfully Your response !!");
        this.sharedDataService.showSuccess("Notification sent successfully!");
      },
      error: (err) => {
        console.error("Error occurred: ", err);
        this.sharedDataService.showError("Notification not sent!");
        this.change = true;

      }
    });
  }
  /**
   * Create notification data for reply
   */
  public reply(): sendNotifyDatamodel {
    return ({
      inquiryQuoteID: this.detailsQuoteData.inquiryQuoteID,
      quoteID: this.detailsQuoteData.quoteID,
      quoteNumber: this.detailsQuoteData.quoteNumber,
      requesterName: this.detailsQuoteData.requesterName,
      requesterEmail: this.detailsQuoteData.requesterEmail,
      supplierName: this.detailsQuoteData.suppliers[0].supplierName,
      supplierEmail: this.detailsQuoteData.suppliers[0].supplierEmail,
      uniqueIdentifier: this.detailsQuoteData.suppliers[0].uniqueIdentifier
    });
  }

  /**
   * Create notification data for sending to account manager
   */
  private notifyData(): any {
    return {
      inquiryQuoteID: this.detailsQuoteData.inquiryQuoteID,
      quoteID: this.detailsQuoteData.quoteID,
      quoteNumber: this.detailsQuoteData.quoteNumber,
      requesterName: this.detailsQuoteData.requesterName,
      requesterEmail: this.detailsQuoteData.requesterEmail,
      supplierName: this.detailsQuoteData.suppliers[0].supplierName,
      supplierEmail: this.detailsQuoteData.suppliers[0].supplierEmail,
      uniqueIdentifier: this.detailsQuoteData.suppliers[0].uniqueIdentifier
    };
  }

  /**
   * Prepare data for API submission
   * - Identifies updated items
   * - Includes parent rows for alternate items
   * - Formats data for API
   */
  private preparePostData(): any | null {
    // First, identify all updated items
    const updatedItems = this.data.filter((item: supplies) => this.isRowUpdated(item));

    // Find parent rows for any alternate rows that are being updated
    const parentRows: supplies[] = [];
    updatedItems.forEach((item: supplies) => {
      if (item.isAlternate) {
        // Find the parent row with the same inquiryMaterialId but not an alternate
        const parentRow = this.data.find(
          (parent) =>
            parent.inquiryMaterialId === item.inquiryMaterialId &&
            !parent.isAlternate
        );

        // Add parent row if not already included
        if (parentRow && !updatedItems.includes(parentRow) && !parentRows.includes(parentRow)) {
          parentRows.push(parentRow);
        }
      }
    });

    // Combine updated items and their parent rows
    const allItemsToSubmit = [...updatedItems, ...parentRows];

    // Map to the required format for API
    const supplierResponses  = allItemsToSubmit.map((item: supplies) => (
    {
      repliesSupplierId: item.repliesSupplierId,
      inquiryMaterialId: item.inquiryMaterialId,
      partModel: item.partModel,
      manufacture: item.manufacture,
      price: item.price,
      rebatePrice: item.rebatePrice,
      supplierComment: item.supplierComment,
      leadTime: item.leadTime,
      isAlternate: item.isAlternate,
      isrebate: item.isrebate,
      supplierUniqueIdentifier: this.detailsQuoteData.suppliers[0].uniqueIdentifier,
    }));

    return supplierResponses.length > 0 ? {
        quoteReference: this.detailsQuoteData.suppliers[0]?.quoteReference || this.QuoteReference,
        supplierResponses
      }
    : null;
  }

  /**
   * Check if a row has been updated compared to original data
   */
  private isRowUpdated(item: supplies): boolean {
    const originalItem = this.originalData.find((orig) => orig.supplieID === item.supplieID);

    if (!originalItem) {
      return true; // New record (not in original data)
    }

    // Compare fields to detect changes
    return (
      item.partModel !== originalItem.partModel ||
      item.price !== originalItem.price ||
      item.manufacture !== originalItem.manufacture ||
      item.rebatePrice !== originalItem.rebatePrice ||
      item.supplierComment !== originalItem.supplierComment ||
      item.leadTime !== originalItem.leadTime ||
      item.isAlternate !== originalItem.isAlternate
    );
  }

  /**
   * Cancel all changes and revert to original data
   */
  public cancelChanges(): void {
    // Reset tracking arrays
    this.reset();

    // Restore original data
    this.data = this.originalData;
    this.originalData = cloneData(this.originalData);

    // Notify subscribers
    super.next(this.data);
  }

  /**
   * Reset tracking arrays
   */
  private reset() {
    this.updatedItems = [];
  }

  // Cache to avoid repeated API calls
  public cachedSupplies: supplies[] = [];
  public cachedSuppliers: supplies[] = [];

  /*
   * Fetch data from the API
   * - Decodes URL parameters if needed
   * - Fetches materials and supplier data
   */
  public fetch(): Observable<supplies[]> {
    // If we already have the inquiry quote ID, fetch data directly
    if (this.inquiryQuoteid) {
      return this.getMaterialAndSupplierData();
    }

    // Otherwise, decode URL parameters first
    return this.coreData.URLDecoder(
      encodeURIComponent(this.secretid),
      encodeURIComponent(this.dataid)
    ).pipe(
      // Extract inquiry quote ID and notification type
      tap((res: any) => {
        this.inquiryQuoteid = res.inquiryQuoteID;
        this.validate = res.notificationType;
      }),

      // Then fetch materials and supplier data
      switchMap(() => this.getMaterialAndSupplierData()),

      // Handle errors
      catchError((err) => {
        console.error("Error in API chain:", err);
        return of([]);
      })
    );
  }

  /**
   * Fetch materials and supplier data using inquiryQuoteID
   * - Combines data from multiple API calls
   * - Merges material and supplier data
   * - Sorts and processes the combined data this.inquiryQuoteID
   */
  private getMaterialAndSupplierData(): Observable<supplies[]> {
    return forkJoin({
      details: this.coreData.details(this.inquiryQuoteid),
      materials: this.coreData.getMaterial(this.inquiryQuoteid),
      suppliers: this.coreData.getSupplier(this.inquiryQuoteid)
    }).pipe(
      map(({ materials, suppliers, details }) => {
        // Cache the data for later use
        this.cachedSupplies = materials;
        console.log("materials",materials );
        this.cachedSuppliers = suppliers.flat();
        this.detailsQuoteData = details;
        console.log("details",details );

        // Set the supplier name in the shared service
        this.sharedDataService.SupplierName = details.suppliers[0].supplierName;
        this.HideGridfield = details.suppliers[0].isrebate; //Hide a price 2 column if supplier is not rebate
        this.QuoteReference = details.suppliers[0].quoteReference;

        // Merge material and supplier data
        let mergedData = this.cachedSupplies.flatMap(material => {
          // Find suppliers for this material
          const supplierData = this.cachedSuppliers.filter(
            supplier => supplier.inquiryMaterialId === material.inquiryMaterialId
          );

          // If suppliers exist, merge with material data
          return supplierData.length > 0
            ? supplierData.map(supplier => ({
              ...supplier,
              ...material,
                // Use supplier values if available, otherwise use material values
                partModel: supplier.partModel?.trim() ? supplier.partModel : material.partModel,
                manufacture: supplier.manufacture ?? material.manufacture,
                isAlternate: supplier.isAlternate ?? false,
              }))
            : [{ ...material, isAlternate: false }];
        });

        // Sort the array to keep alternate rows below parent rows
        // and delivery cost at the end
        mergedData.sort((a, b) => {
          if (a.productName === "Delivery Cost") return 1;
          if (b.productName === "Delivery Cost") return -1;

          if (a.inquiryMaterialId === b.inquiryMaterialId) {
            return a.isAlternate === b.isAlternate ? 0 : a.isAlternate ? 1 : -1;
          }
          return 0;
        });

        // Add supplieID and revised flag to each item
        mergedData = mergedData.map((item, index) => {
          const supplieID = index + 1;

          // Get dates for revision check
          const supplierLastResponded = this.detailsQuoteData.suppliers[0].supplierLastResponded;
          const creationDate = item.creationDate;
          const updationDate = item.updationDate;

          // Determine if the item is revised
          const revised = supplierLastResponded < creationDate || supplierLastResponded < updationDate;
          // ❌ Remove attachments from alternate rows
          const attachments = item.isAlternate ? [] : item.attachments;
          console.log("supplierLastResponded", supplierLastResponded , "updationDate", updationDate, "revised", revised);

          return {
            ...item,
            attachments,
            supplieID,
            revised,
          };
        });
        console.log("mergedData",mergedData );
        return mergedData;
      })
    );
  }

  /**
   * Process data for updates
   * - Recreates the data structure
   * - Applies updates to specific items
   */
  public resultData: supplies[] = [];
  private processData(action: string, data?: supplies[]): Observable<supplies[]> {
    // Recreate the data structure from cached supplies and suppliers
    let supplieIDCounter = 1;
    this.resultData = this.cachedSupplies.flatMap(material => {
      const supplierData = this.cachedSuppliers.filter(
        supplier => supplier.inquiryMaterialId === material.inquiryMaterialId
      );

      return supplierData.length > 0
        ? supplierData.map(supplier => ({
            ...supplier,
            ...material,
            supplieID: supplieIDCounter++,
            partModel: supplier.partModel?.trim() ? supplier.partModel : material.partModel,
          }))
        : [{ ...material, supplieID: supplieIDCounter++ }];
    });

    // Apply updates if action is "update" and data is provided
    if (action === "update" && data) {
      this.resultData = this.resultData.map((item) => {
        const updatedItem = data.find((d) => d.supplieID === item.supplieID);
        return updatedItem ? { ...item, ...updatedItem } : item;
      });
      this.cachedSupplies = [...this.resultData];
    }

    return of(this.resultData);
  }
}
